from tkinter import *
from tkinter import messagebox as msgbox 
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)


        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        try:
            # Check if attendance.csv exists, create if not
            import os
            if not os.path.exists("attendance.csv"):
                with open("attendance.csv", "w", newline="") as f:
                    f.write("Name,Roll,Department,ID,Time,Date,Status\n")

            with open("attendance.csv", "r+", newline="") as f:
                myDataList = f.readlines()
                name_list = []
                for line in myDataList:
                    entry = line.strip().split(",")
                    if len(entry) >= 4:
                        name_list.append(entry[3])  # Check by ID to avoid duplicates

                # Check if this ID already marked attendance today
                now = datetime.now()
                d1 = now.strftime("%d/%m/%Y")

                already_marked = False
                for line in myDataList:
                    entry = line.strip().split(",")
                    if len(entry) >= 6 and entry[3] == str(id) and entry[5] == d1:
                        already_marked = True
                        break

                if not already_marked:
                    dtString = now.strftime("%H:%M:%S")
                    f.write(f"{n},{r},{d},{id},{dtString},{d1},Present\n")
                    print(f"Attendance marked for {n}")
                else:
                    print(f"Attendance already marked for {n} today")
        except Exception as e:
            print(f"Error marking attendance: {e}")

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors, color, text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)

            for (x, y, w, h) in features:
                cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)

                try:
                    id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                    confidence = int(100 * (1 - predict / 300))

                    if confidence > 77:
                        try:
                            # Database connection with error handling
                            conn = mysql.connector.connect(
                                host="localhost",
                                username="root",
                                password="Admin@1402",
                                database="face_recognizer"
                            )
                            my_cursor = conn.cursor()

                            # Get student details
                            my_cursor.execute("SELECT Name FROM student WHERE Student_id=%s", (id,))
                            n_result = my_cursor.fetchone()
                            n = n_result[0] if n_result else "Unknown"

                            my_cursor.execute("SELECT Roll FROM student WHERE Student_id=%s", (id,))
                            r_result = my_cursor.fetchone()
                            r = r_result[0] if r_result else "Unknown"

                            my_cursor.execute("SELECT Dep FROM student WHERE Student_id=%s", (id,))
                            d_result = my_cursor.fetchone()
                            d = d_result[0] if d_result else "Unknown"

                            conn.close()

                            # Display recognized student info
                            cv2.putText(img, f"ID: {id}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Name: {n}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Roll: {r}", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Dept: {d}", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)

                            # Mark attendance only once per day
                            self.mark_attendance(n, r, d, id)

                        except mysql.connector.Error as db_error:
                            print(f"Database error: {db_error}")
                            cv2.putText(img, "DB Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                        except Exception as e:
                            print(f"Error getting student details: {e}")
                            cv2.putText(img, "Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                    else:
                        # Unknown face - red rectangle
                        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                        cv2.putText(img, "Unknown Face", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                        cv2.putText(img, f"Confidence: {confidence}%", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 0, 255), 2)

                except Exception as e:
                    print(f"Face recognition error: {e}")
                    cv2.putText(img, "Recognition Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)

            return img

        def recognize(img, clf, faceCascade):
            img = draw_boundary(img, faceCascade, 1.1, 10, (255, 255, 255), "Face", clf)
            return img

        try:
            # Load cascade classifier
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Could not load face cascade classifier!")
                return

            # Load trained model
            clf = cv2.face.LBPHFaceRecognizer_create()
            try:
                clf.read("classifier.xml")
            except:
                msgbox.showerror("Error", "Could not load trained model! Please train the model first.")
                return

            # Initialize camera
            video_capture = cv2.VideoCapture(0)
            if not video_capture.isOpened():
                msgbox.showerror("Error", "Could not open camera!")
                return

            print("Face recognition started. Press Enter to stop.")

            while True:
                ret, img = video_capture.read()
                if not ret:
                    print("Failed to capture frame")
                    break

                img = recognize(img, clf, faceCascade)
                cv2.imshow("Face Recognition System", img)

                # Press Enter (key 13) to exit
                if cv2.waitKey(1) & 0xFF == 13:
                    break

        except Exception as e:
            msgbox.showerror("Error", f"Face recognition error: {str(e)}")
        finally:
            try:
                video_capture.release()
                cv2.destroyAllWindows()
            except:
                pass
    
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          