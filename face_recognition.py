
from tkinter import *
from tkinter import messagebox as msgbox 
from PIL import Image, ImageTk
from datetime import datetime
import cv2
import mysql.connector
class Face_Recognition:
    def __init__(self, root):
        self.root = root
        self.root.geometry("1530x790+0+0")
        self.root.title("Face Recognition")

        # Title label
        title_lbl = Label(self.root, text="FACE RECOGNITION", font=("arial", 25, "bold"), bg="white", fg="red")
        title_lbl.place(x=0, y=0, width=1530, height=47)

        # First image
        img_left = Image.open(r"Project_images\face_detector1.jpg")
        img_left = img_left.resize((650, 700), Image.LANCZOS)
        self.photo_left = ImageTk.PhotoImage(img_left)
        f_lbl_left = Label(self.root, image=self.photo_left)
        f_lbl_left.place(x=0, y=55, width=650, height=700)


        # Second image
        img_right = Image.open(r"Project_images\facial_recognition_system_identification_digital_id_security_scanning_thinkstock_858236252_3x3-100740902-large.jpg")
        img_right = img_right.resize((950, 700), Image.LANCZOS)
        self.photo_right = ImageTk.PhotoImage(img_right)
        f_lbl_right = Label(self.root, image=self.photo_right)
        f_lbl_right.place(x=650, y=55, width=950, height=700)

        # Face Recognition Button
        Button(self.root, text="FACE RECOGNITION", command=self.face_recog, cursor="hand2",
               font=("times new roman", 15, "bold"), bg="darkgreen", fg="white").place(x=1012, y=680, width=220, height=41)
#===============Attendance=================
    def mark_attendance(self, n, r, d, id):
        try:
            # Save to database
            self.save_to_database(n, r, d, id)

            # Also save to CSV file
            import os
            if not os.path.exists("attendance.csv"):
                with open("attendance.csv", "w", newline="") as f:
                    f.write("Name,Roll,Department,ID,Time,Date,Status\n")

            with open("attendance.csv", "r+", newline="") as f:
                myDataList = f.readlines()

                # Check if already marked today
                now = datetime.now()
                d1 = now.strftime("%d/%m/%Y")

                already_marked = False
                for line in myDataList:
                    entry = line.strip().split(",")
                    if len(entry) >= 6 and entry[3] == str(id) and entry[5] == d1:
                        already_marked = True
                        break

                if not already_marked:
                    dtString = now.strftime("%H:%M:%S")
                    f.write(f"{n},{r},{d},{id},{dtString},{d1},Present\n")
                    print(f"✓ Attendance marked for {n}")
                else:
                    print(f"⚠ Attendance already marked for {n} today")

        except Exception as e:
            print(f"Error marking attendance: {e}")

    def save_to_database(self, name, roll, dept, student_id):
        """Save attendance to database"""
        try:
            conn = mysql.connector.connect(
                host="localhost",
                username="root",
                password="Admin@1402",
                database="face_recognizer"
            )
            cursor = conn.cursor()

            # Check if attendance table exists, create if not
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS attendance (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    student_id VARCHAR(50),
                    name VARCHAR(100),
                    roll_no VARCHAR(50),
                    department VARCHAR(100),
                    date DATE,
                    time TIME,
                    status VARCHAR(20) DEFAULT 'Present',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)

            # Check if already marked today
            today = datetime.now().date()
            cursor.execute("""
                SELECT id FROM attendance
                WHERE student_id = %s AND date = %s
            """, (student_id, today))

            if cursor.fetchone() is None:
                # Insert new attendance record
                now = datetime.now()
                cursor.execute("""
                    INSERT INTO attendance (student_id, name, roll_no, department, date, time, status)
                    VALUES (%s, %s, %s, %s, %s, %s, %s)
                """, (student_id, name, roll, dept, now.date(), now.time(), 'Present'))

                conn.commit()
                print(f"✓ Attendance saved to database for {name}")
            else:
                print(f"⚠ Database attendance already exists for {name} today")

            conn.close()

        except mysql.connector.Error as e:
            print(f"Database error: {e}")
        except Exception as e:
            print(f"Error saving to database: {e}")

#==========Face Recognition Function==========
    def face_recog(self):
        def draw_boundary(img, classifier, scaleFactor, minNeighbors, color, text, clf):
            gray_image = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)
            features = classifier.detectMultiScale(gray_image, scaleFactor, minNeighbors)

            for (x, y, w, h) in features:
                try:
                    id, predict = clf.predict(gray_image[y:y+h, x:x+w])
                    confidence = int(100 * (1 - predict / 300))

                    if confidence > 77:
                        try:
                            # Database connection with error handling
                            conn = mysql.connector.connect(
                                host="localhost",
                                username="root",
                                password="Admin@1402",
                                database="face_recognizer"
                            )
                            my_cursor = conn.cursor()

                            # Get student details safely
                            my_cursor.execute("SELECT Name FROM student WHERE Student_id=%s", (id,))
                            n_result = my_cursor.fetchone()
                            n = n_result[0] if n_result else "Unknown"

                            my_cursor.execute("SELECT Roll FROM student WHERE Student_id=%s", (id,))
                            r_result = my_cursor.fetchone()
                            r = r_result[0] if r_result else "Unknown"

                            my_cursor.execute("SELECT Dep FROM student WHERE Student_id=%s", (id,))
                            d_result = my_cursor.fetchone()
                            d = d_result[0] if d_result else "Unknown"

                            conn.close()

                            # Green rectangle for recognized face
                            cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 0), 3)
                            cv2.putText(img, f"ID: {id}", (x, y-75), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Name: {n}", (x, y-55), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Roll: {r}", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)
                            cv2.putText(img, f"Dept: {d}", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.8, (255, 255, 255), 3)

                            # Mark attendance
                            self.mark_attendance(n, r, d, id)

                        except mysql.connector.Error as db_error:
                            print(f"Database error: {db_error}")
                            cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 255), 3)
                            cv2.putText(img, "DB Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 3)
                        except Exception as e:
                            print(f"Error getting student details: {e}")
                            cv2.rectangle(img, (x, y), (x+w, y+h), (0, 255, 255), 3)
                            cv2.putText(img, "Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 255, 255), 3)
                    else:
                        # Red rectangle for unknown face
                        cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                        cv2.putText(img, "Unknown Face", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)
                        cv2.putText(img, f"Confidence: {confidence}%", (x, y-5), cv2.FONT_HERSHEY_COMPLEX, 0.6, (0, 0, 255), 2)

                except Exception as e:
                    print(f"Face recognition error: {e}")
                    cv2.rectangle(img, (x, y), (x+w, y+h), (0, 0, 255), 3)
                    cv2.putText(img, "Recognition Error", (x, y-30), cv2.FONT_HERSHEY_COMPLEX, 0.8, (0, 0, 255), 3)

            return img

        def recognize(img, clf, faceCascade):
            img = draw_boundary(img, faceCascade, 1.1, 10, (255, 255, 255), "Face", clf)
            return img

        try:
            # Load cascade classifier
            faceCascade = cv2.CascadeClassifier("haarcascade_frontalface_default.xml")
            if faceCascade.empty():
                msgbox.showerror("Error", "Could not load face cascade classifier!\nMake sure 'haarcascade_frontalface_default.xml' exists.")
                return

            # Load trained model
            clf = cv2.face.LBPHFaceRecognizer_create()
            try:
                clf.read("classifier.xml")
            except:
                msgbox.showerror("Error", "Could not load trained model!\nPlease train the model first using 'Train Data' button.")
                return

            # Initialize camera with multiple attempts
            video_capture = None
            camera_opened = False

            # Try different camera indices and backends
            camera_indices = [0, 1, 2, -1]
            backends = [cv2.CAP_DSHOW, cv2.CAP_MSMF, cv2.CAP_ANY]

            print("🔍 Attempting to open camera...")

            for cam_idx in camera_indices:
                for backend in backends:
                    try:
                        print(f"Trying camera {cam_idx} with backend {backend}")
                        video_capture = cv2.VideoCapture(cam_idx, backend)

                        if video_capture.isOpened():
                            # Set camera properties
                            video_capture.set(cv2.CAP_PROP_FRAME_WIDTH, 640)
                            video_capture.set(cv2.CAP_PROP_FRAME_HEIGHT, 480)
                            video_capture.set(cv2.CAP_PROP_FPS, 30)
                            video_capture.set(cv2.CAP_PROP_BUFFERSIZE, 1)

                            # Test if we can actually read a frame
                            ret, test_frame = video_capture.read()
                            if ret and test_frame is not None:
                                print(f"✅ Camera opened successfully with index {cam_idx}")
                                camera_opened = True
                                break
                            else:
                                video_capture.release()

                    except Exception as e:
                        print(f"❌ Failed with camera {cam_idx}: {e}")
                        if video_capture:
                            video_capture.release()
                        continue

                if camera_opened:
                    break

            if not camera_opened:
                msgbox.showerror("Camera Error",
                    "❌ Could not open camera!\n\n"
                    "🔧 Troubleshooting:\n"
                    "1. Check if camera is connected\n"
                    "2. Close other apps using camera (Skype, Teams, etc.)\n"
                    "3. Try running as administrator\n"
                    "4. Check Windows camera permissions\n"
                    "5. Test camera in Windows Camera app first")
                return

            print("🎥 Face recognition started!")
            print("📝 Press 'q', 'Q', Enter, or ESC to stop")

            while True:
                ret, img = video_capture.read()
                if not ret or img is None:
                    print("❌ Failed to capture frame")
                    break

                img = recognize(img, clf, faceCascade)
                cv2.imshow("Face Recognition System - Press 'q' to quit", img)

                # Multiple exit options
                key = cv2.waitKey(1) & 0xFF
                if key == ord('q') or key == ord('Q') or key == 13 or key == 27:
                    break

        except Exception as e:
            msgbox.showerror("Error", f"Face recognition error: {str(e)}")
        finally:
            try:
                if 'video_capture' in locals():
                    video_capture.release()
                cv2.destroyAllWindows()
                print("🔒 Camera released and windows closed")
            except:
                pass
    
       
if __name__ == "__main__":
    root=Tk()
    obj=Face_Recognition(root)
    root.mainloop()          
